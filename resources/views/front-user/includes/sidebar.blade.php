@if (Auth::user()->user_type == 'influencer')

    {{-- ----@php $Influencer = App\Models\InfluencerDetail::where('user_id',Auth::user()->id)->first();
        if(isset($Influencer)){
             $req_count = App\Models\InfluencerRequestDetail::where('influencer_detail_id',$Influencer->id)
                        ->where('refund_reason',NULL)->where('finish',NULL)
                        ->where(function ($query) {
                            $query->where('review', '=', NULL)
                                  ->orWhere('review', '=', 0);
                        })
                        ->count();
        }else{
            $req_count = 0;
        }

            $tot_count = App\Models\InfluencerRequestDetail::where('influencer_detail_id',@$Influencer->id)
        ->where('finish',NULL)->where('refund_reason',NULL)
        ->where(function ($query) {
            $query->where('review', '=', NULL)
                  ->orWhere('review', '=', 0);
        })
        ->count();


             $open_count = App\Models\InfluencerRequestDetail::join('influencer_request_accepts',  'influencer_request_accepts.influencer_request_detail_id', '=', 'influencer_request_details.id' )
             ->select('influencer_request_details.*')
             ->where('influencer_request_details.influencer_detail_id',@$Influencer->id)
             ->where('influencer_request_details.finish',NULL)
             ->where('influencer_request_details.refund_reason',NULL)
            ->where(function ($query) {
                $query->where('influencer_request_details.review', '=', NULL)
                      ->orWhere('influencer_request_details.review', '=', 0);
            })
             ->count();



          $tot =$tot_count-$open_count;


         @endphp  --- --}}
    @php

        $Influencer = App\Models\InfluencerDetail::where('user_id', Auth::id())->first();

        $tot_count = App\Models\InfluencerRequestDetail::where('influencer_detail_id', @$Influencer->id)
            ->where('finish', null)
            ->where('refund_reason', null)
            ->where(function ($query) {
                $query->where('review', '!=', 1);
            })
            ->count();
        $open_count = App\Models\InfluencerRequestDetail::join(
            'influencer_request_accepts',
            'influencer_request_accepts.influencer_request_detail_id',
            '=',
            'influencer_request_details.id',
        )
            ->select('influencer_request_details.*')
            ->where('influencer_request_details.influencer_detail_id', @$Influencer->id)
            ->where('influencer_request_details.finish', null)
            ->where('influencer_request_details.refund_reason', null)
            ->where(function ($query) {
                $query
                    ->where('influencer_request_details.review', '=', null)
                    ->orWhere('influencer_request_details.review', '=', 0);
            })
            ->count();

        $req_count = $tot_count;

        $myCampaignList = App\Models\InfluencerRequestDetail::join(
            'influencer_details',
            'influencer_request_details.influencer_detail_id',
            '=',
            'influencer_details.id',
        )
            ->leftjoin('users', 'users.id', '=', 'influencer_details.user_id')
            ->leftjoin(
                'influencer_request_accepts',
                'influencer_request_accepts.influencer_request_detail_id',
                '=',
                'influencer_request_details.id',
            )
            ->select(
                'influencer_request_details.*',
                'influencer_details.id as i_id',
                'influencer_details.user_id as i_user_id',
                'influencer_request_accepts.request',
                'influencer_request_accepts.request_time',
                'influencer_request_accepts.request_time_accept',
                'influencer_request_accepts.id as influencer_request_accept_id',
                'influencer_request_accepts.created_at as accept_time',
            )
            ->where('influencer_details.user_id', Auth::id())
            ->orderBy('influencer_request_details.id', 'desc')
            ->get();

        $postCount = 0;
        $postCountReq = 0;
        foreach ($myCampaignList as $row) {
            if (
                $row->request == 1 &&
                $row->invoice_id != '' &&
                $row->review != '1' &&
                $row->review != '0' &&
                $row->refund_reason == ''
            ) {
                $postCountReq++;
                if ($row->invoice_id != '') {
                    $postCount++;
                }
            }
        }

        $reqCountList = App\Models\InfluencerRequestDetail::join(
            'influencer_details',
            'influencer_request_details.influencer_detail_id',
            '=',
            'influencer_details.id',
        )
            ->leftjoin('users', 'users.id', '=', 'influencer_details.user_id')
            ->leftjoin(
                'influencer_request_accepts',
                'influencer_request_accepts.influencer_request_detail_id',
                '=',
                'influencer_request_details.id',
            )
            ->leftjoin('campaigns', 'campaigns.campaign_id', '=', 'influencer_request_details.compaign_id')
            ->select(
                'influencer_request_details.*',
                'campaigns.has_started',
                'influencer_details.id as i_id',
                'influencer_details.user_id as i_user_id',
                'influencer_request_accepts.request',
                'influencer_request_accepts.request_time',
                'influencer_request_accepts.request_time_accept',
                'influencer_request_accepts.id as influencer_request_accept_id',
            )
            ->where('influencer_details.user_id', Auth::id())
            ->where('campaigns.has_started', false)
            ->orderBy('influencer_request_details.id', 'desc')
            ->get();

        $campaignRequestTime = App\Models\CampaignRequestTime::first();

        $reqCount = 0;
        $openCountReq = 0;
        foreach ($reqCountList as $row) {
            if ($row->invoice_id == '' && $row->review != '2' && $row->refund_reason == '' && $row->status != 'Cancelled') {

                $created_date = date('Y-m-d', strtotime($row->created_at));
                $campaignDate = date('Y-m-d', strtotime($created_date . '+3 days'));
                $date = date('Y-m-d');
                if ($date <= $campaignDate) {
                    $openCountReq++;
                    $reqCount++;
                }
            }
        }

        $tot = $reqCount;
        $req_count = $postCountReq + $openCountReq;
        // influencer my campagins
        $infTotalActiveCampaigns = 0;
        $influencerActiveCampaigns = App\Models\InfluencerRequestDetail::join(
            'influencer_details',
            'influencer_request_details.influencer_detail_id',
            '=',
            'influencer_details.id',
        )
            ->leftjoin('users', 'users.id', '=', 'influencer_details.user_id')
            ->leftjoin(
                'influencer_request_accepts',
                'influencer_request_accepts.influencer_request_detail_id',
                '=',
                'influencer_request_details.id',
            )
            ->select(
                'influencer_request_details.*',
                'influencer_details.id as i_id',
                'influencer_details.user_id as i_user_id',
                'influencer_request_accepts.request',
                'influencer_request_accepts.request_time',
                'influencer_request_accepts.request_time_accept',
                'influencer_request_accepts.id as influencer_request_accept_id',
                'influencer_request_accepts.created_at as accept_time',
            )
            ->where('influencer_details.user_id', Auth::id())
            ->orderBy('influencer_request_details.id', 'desc')
            ->get();
        foreach ($influencerActiveCampaigns as $row) {
            if (
                $row->request == 1 &&
                $row->invoice_id != '' &&
                $row->review != '1' &&
                $row->refund_reason == '' &&
                $row->finish != '1'
            ) {
                $infTotalActiveCampaigns++;
            }
        }
    @endphp

    <div class="dashboardsidebar @if (Auth::user()->status == 1 && $req_count < 5) colOnline @else colOffline @endif">
    @else
        @php
            // customer campaigns counter
            $influencerData = App\Models\InfluencerRequestDetail::join(
                'influencer_details',
                'influencer_request_details.influencer_detail_id',
                '=',
                'influencer_details.id',
            )
                ->leftjoin('users', 'users.id', '=', 'influencer_details.user_id')
                ->leftjoin(
                    'influencer_request_accepts',
                    'influencer_request_accepts.influencer_request_detail_id',
                    '=',
                    'influencer_request_details.id',
                )
                ->leftjoin(
                    'campaigns',
                    'campaigns.campaign_id',
                    '=',
                    'influencer_request_details.compaign_id'
                )
                ->select(
                    'influencer_request_details.*',
                    'campaigns.has_started',
                    'influencer_details.id as i_id',
                    'influencer_details.user_id as i_user_id',
                    'influencer_request_accepts.request',
                    'influencer_request_accepts.id as influencer_request_accept_id',
                    DB::raw('COUNT(influencer_request_accepts.id) AS total_count'),
                    DB::raw('SUM(influencer_request_accepts.request) AS accept_request'),
                    DB::raw('COUNT(influencer_request_details.id) AS total_influencer_count'),
                )
                ->where('influencer_request_details.user_id', Auth::id())
                ->where('influencer_request_details.status', null)
                ->where('campaigns.has_started', false)
                ->groupBy('influencer_request_details.compaign_id')
                ->orderBy('influencer_request_details.id', 'desc')
                ->get();

            $open_campaigns = 0;
            if (isset($influencerData)) {
                foreach ($influencerData as $row) {
                    if ($row->accept_request > 0 || $row->review != '2' && $row->finish != '0') {
                        $open_campaigns++;
                    }
                }
            }
            $influencerDataActive = App\Models\InfluencerRequestDetail::join(
                'influencer_details',
                'influencer_request_details.influencer_detail_id',
                '=',
                'influencer_details.id',
            )
                ->leftjoin('users', 'users.id', '=', 'influencer_details.user_id')
                ->leftjoin(
                    'influencer_request_accepts',
                    'influencer_request_accepts.influencer_request_detail_id',
                    '=',
                    'influencer_request_details.id',
                )
                ->select(
                    'influencer_request_details.*',
                    'influencer_details.id as i_id',
                    'influencer_details.user_id as i_user_id',
                    'influencer_request_accepts.request',
                    'influencer_request_accepts.id as influencer_request_accept_id',
                    DB::raw('COUNT(influencer_request_accepts.id) AS total_count'),
                    DB::raw('SUM(influencer_request_accepts.request) AS accept_request'),
                    DB::raw('COUNT(influencer_request_details.id) AS total_influencer_count'),
                )
                ->where('influencer_request_details.user_id', Auth::id())
                ->groupBy('influencer_request_details.compaign_id')
                ->orderBy('influencer_request_details.id', 'desc')
                ->get();

            $active_campaigns = 0;
            foreach ($influencerDataActive as $row) {
                if ($row->invoice_id != null && $row->finish != '1' && $row->status != 'Cancelled') {
                    $active_campaigns++;
                }
            }
        @endphp

        <div class="dashboardsidebar">
@endif
<div class="side-top">
    <input type="hidden" id="verified" value="{{ Auth::user()->email_verified_at }}">
    <input type="hidden" id="published" @if (isset(Auth::user()->influencerDetail)) value="{{ Auth::user()->influencerDetail->publish }}" @endif>

    @if (Auth::user()->user_type == 'influencer')
        <input type="hidden" id="req_count" value="{{ $req_count }}">
    @endif
</div>
<div class="menudiv">
    <ul class="main-menu">
        <li>
            <div class="user_profile_pic">
                @php
                    if (Auth::user()->profile_pic == '' || Auth::user()->profile_pic == null) {
                        $src = URL::asset('/assets/front-end/images/icons/default-profile-pic.png');
                    } else {
                        $src = \Illuminate\Support\Facades\Storage::url(Auth::user()->profile_pic);
                    }
                @endphp
                <div class="img-usr">
                    <img src="{{ $src }}" alt="">
                </div>
                <span style="padding-top: 7px;width:auto !important;"
                    class="lki">{{ Auth::user()->first_name }}</span>
            </div>
            <div class="toggleLink me-4">
                <img src="{{ asset('/assets/front-end/images/icons/arrow_toggle.svg') }}" alt="">
            </div>
        </li>

        <li
            class="home-link @if (Auth::user()->user_type == 'influencer') @if (isset(Auth::user()->influencerDetail->publish) && Auth::user()->influencerDetail->publish == 'Publish')  @else desabled @endif @endif">
            <a href="{{ url('/home') }}" class="{{ Request::is('home') || Request::is('/') ? 'active' : '' }}"
                @if (isset(Auth::user()->influencerDetail->publish) && Auth::user()->influencerDetail->publish == 'Publish') @else  desabled @endif>
                <img src="{{ asset('/assets/front-end/images/icons/sidemenu-home_new.svg') }}" alt=""
                    style="height: 24px;width:24px;">
                <span class="lki">Home
                </span></a>


        </li>

        @if (Auth::user()->user_type == 'customer')
            <li>
                <a @if (Request::is('my-profile')) href="javascript:void(0)" @else href="{{ url('my-profile') }}" @endif
                    class="{{ Request::is('my-profile') ? 'active' : '' }}">
                    <img src="{{ asset('/assets/front-end/images/icons/profile_info_new.svg') }}"
                        style="height: 24px;width:24px;" class="profile_svg" alt="">
                    <span class="lki d-flex">Profile Information

                    </span>
                </a>
            </li>
        @endif

        @if (Auth::user()->user_type == 'influencer')
            <li>
                <a @if (Request::is('influencer-onboarding')) href="javascript:void(0)" @else  href="{{ url('influencer-onboarding') }}" @endif
                    class="{{ Request::is('influencer-onboarding') ? 'active' : '' }} ">
                    @if (isset(Auth::user()->influencerDetail->publish) && Auth::user()->influencerDetail->publish == 'Publish')
                        <span class="datatip" data-tip="Your matketplace status is Online.">
                            <img src="{{ asset('/assets/front-end/images/icons/onboarding_icon.svg') }}"
                                alt="">
                        </span>
                    @else
                        <span class="datatip" data-tip="Your matketplace status is currently not online.">
                            <img src="{{ asset('/assets/front-end/images/icons/onboarding_icon.svg') }}"
                                alt="">
                        </span>
                    @endif
                    <span class="lki d-flex">
                        Onboarding
                    </span>
                </a>
            </li>
        @endif
        <li class="@if (Auth::user()->user_type == 'influencer') @if (isset(Auth::user()->influencerDetail->publish) && Auth::user()->influencerDetail->publish == 'Publish')  @else  desabled @endif @endif">

            


                @if (Auth::user()->user_type == 'customer')
                    <a href="{{ url('open-campaigns') }}" class="{{ Request::is('open-campaigns') || Request::is('request-history') ? 'active' : '' }}">
                        <img src="{{ asset('/assets/front-end/images/icons/sidemenu-requests_new.svg') }}" alt="" style="height: 24px;width:24px;">
                        <span class="lki"> Open campaigns
                            <span class="reqCont">{{ isset($open_campaigns) && $open_campaigns > 0 ? $open_campaigns : 0 }}</span>
                        </span>
                    </a>
                @else
                    <a href="{{ url('open-requests') }}" class="{{ Request::is('open-requests') || Request::is('request-history') ? 'active' : '' }}">
                        <img src="{{ asset('/assets/front-end/images/icons/sidemenu-request-ticket-star.svg') }}" alt="" style="height: 24px;width:24px;">
                        <span class="lki"> Requests
                            <span class="reqCont">{{ isset($tot) && $tot > 0 ? $tot : 0 }}</span>
                        </span>
                    </a>
                @endif
        </li>
        <li
            class="@if (Auth::user()->user_type == 'influencer') @if (isset(Auth::user()->influencerDetail->publish) && Auth::user()->influencerDetail->publish == 'Publish')  @else  desabled @endif @endif">

            <a href="{{ url('active-campaigns') }}"
                class="{{ Request::is('active-campaigns') || Request::is('campaign-history') ? 'active' : '' }}">


                @if (Auth::user()->user_type == 'customer')
                    <img src="{{ asset('/assets/front-end/images/icons/active_campaigns_message.svg') }}"
                        alt="" style="height: 24px;width:24px;">
                    <span class="lki">
                        Active campaigns
                        <span
                            class="reqCont">{{ isset($active_campaigns) && $active_campaigns > 0 ? $active_campaigns : 0 }}</span>
                    </span>
                @else
                    <img src="{{ asset('/assets/front-end/images/icons/sidemenu-requests_new.svg') }}"
                        alt="" style="height: 24px;width:24px;">
                    <span class="lki">
                        My Campaigns
                        <span
                            class="reqCont">{{ isset($infTotalActiveCampaigns) && $infTotalActiveCampaigns > 0 ? $infTotalActiveCampaigns : 0 }}</span>
                    </span>
                @endif
            </a>
        </li>

        @if (Auth::user()->user_type == 'influencer')
            <li class="@if (isset(Auth::user()->influencerDetail->publish) && Auth::user()->influencerDetail->publish == 'Publish') @else  desabled @endif ">
                <a href="{{ url('cash-out') }}" class="{{ Request::is('cash-out') ? 'active' : '' }}">
                    <img src="{{ asset('/assets/front-end/images/icons/sidemenu-cashout.svg') }}" alt="">
                    <span class="lki">Cash out</span>
                </a>
            </li>

        @endif
        @if (Auth::user()->user_type == 'influencer')
            <li class="@if (isset(Auth::user()->influencerDetail->publish) && Auth::user()->influencerDetail->publish == 'Publish') @else desabled @endif ">
                <a href="{{ url('my-rank') }}" class="{{ Request::is('my-rank') ? 'active' : '' }}">
                    <img src="{{ asset('/assets/front-end/images/icons/sidemenu-statistics.svg') }}"
                        alt="">
                    <span class="lki">My Rank</span>
                </a>
            </li>
        @endif
        @if (Auth::user()->user_type == 'customer')
            <li class="desabled">
                <a href="{{ url('statistics') }}" class="{{ Request::is('statistics') ? 'active' : '' }}">
                    <img src="{{ asset('/assets/front-end/images/icons/analysis.svg') }}" alt=""
                        style="height: 27px;width:27px;">
                    <span class="lki">Analysis</span>
                </a>
            </li>
        @endif

        <?php
        $requiredFields = 0;
        if (Auth::user()->first_name == null) {
            $requiredFields += 1;
        }
        if (Auth::user()->last_name == null) {
            $requiredFields += 1;
        }
        if (Auth::user()->country == null) {
            $requiredFields += 1;
        }
        if (Auth::user()->city == null) {
            $requiredFields += 1;
        }

        $optionalFields = 0;
        if (Auth::user()->phone == null) {
            $optionalFields += 1;
        }

        if (Auth::user()->company_name == null) {
            $optionalFields += 1;
        }

        if (Auth::user()->street == null) {
            $optionalFields += 1;
        }

        if (Auth::user()->zip_code == null) {
            $optionalFields += 1;
        }

        ?>
        <li>
            <a href="{{ url('logout') }}">
                <img src="{{ asset('/assets/front-end/images/icons/logout-icon.svg') }}" alt="" style="height: 24px;width:24px;">
                <span class="lki">Logout</span>
            </a>
        </li>
    </ul>
</div>
</div>

<div class="mobile-menu">
    <div class="menu-overlay"></div>
    @if (Auth::user()->user_type == 'influencer')
        <div class="menu-influncer">
            <div class="fixed-menu">
                <ul class="">
                    <li
                        class="@if (Auth::user()->user_type == 'influencer') @if (isset(Auth::user()->influencerDetail->publish) && Auth::user()->influencerDetail->publish == 'Publish') home-link @else home-link desabled @endif @endif">
                        <a href="{{ url('/home') }}" class="{{ Request::is('/home') ? 'active' : '' }}">
                            <img src="{{ asset('/assets/front-end/images/icons/sidemenu-home_new.svg') }}" alt="">
                            <span class="lki">Home</span>
                        </a>
                    </li>
                    <li
                        class="@if (Auth::user()->user_type == 'influencer') @if (isset(Auth::user()->influencerDetail->publish) && Auth::user()->influencerDetail->publish == 'Publish')  @else  desabled @endif @endif">

                        <a href="{{ url('active-campaigns') }}"
                            class="{{ Request::is('active-campaigns') ? 'active' : '' }}">
                            <img src="{{ asset('/assets/front-end/images/icons/sidemenu-requests_new.svg') }}"
                                alt="">
                            <span class="lki">Campaigns
                            </span></a>
                    </li>
                    <li
                        class="@if (Auth::user()->user_type == 'influencer') @if (isset(Auth::user()->influencerDetail->publish) && Auth::user()->influencerDetail->publish == 'Publish')  @else  desabled @endif @endif">

                        <a href="{{ url('open-campaigns') }}"
                            class="{{ Request::is('open-campaigns') ? 'active' : '' }}">
                            <div class="ing-io">
                                <span class="reqCont">{{ $tot > 0 ? $tot : 0 }}</span>
                                <img src="{{ asset('/assets/front-end/images/icons/sidemenu-request-ticket-star.svg') }}"
                                    alt="">
                            </div>
                            <span class="lki">Requests
                            </span>
                        </a>
                    </li>
                    <li class="@if (isset(Auth::user()->influencerDetail->publish) && Auth::user()->influencerDetail->publish == 'Publish') @else  desabled @endif ">
                        <a href="{{ url('cash-out') }}" class="{{ Request::is('cash-out') ? 'active' : '' }}">
                            <img src="{{ asset('/assets/front-end/images/icons/sidemenu-cashout.svg') }}"
                                alt="">
                            <span class="lki">Cash out</span>
                        </a>
                    </li>
                    <li class="extramenu">
                        <a href="javascript:void(0)" class="open-extra">
                            <img src="{{ asset('/assets/front-end/images/icons/mobile-menu-icon.svg') }}"
                                alt="">
                            <span class="lki">Menu</span>
                        </a>
                    </li>
                </ul>
            </div>
            <div class="extra-menu">
                <ul class="">
                    <li class="@if ($requiredFields != '0') desabled @endif ">
                        <a @if (Request::is('influencer-onboarding')) href="javascript:void(0)" @else  href="{{ url('influencer-onboarding') }}" @endif
                            class="{{ Request::is('influencer-onboarding') ? 'active' : '' }} ">
                            @if (isset(Auth::user()->influencerDetail->publish) && Auth::user()->influencerDetail->publish == 'Publish')
                                <span class="datatip" data-tip="Your matketplace status is Online.">
                                    <img src="{{ asset('/assets/front-end/images/icons/sidemenu-heart.svg') }}"
                                        alt="">
                                </span>
                            @else
                                <span class="datatip" data-tip="Your matketplace status is currently not online.">
                                    <img src="{{ asset('/assets/front-end/images/icons/sidemenu-info.svg') }}"
                                        alt="">
                                </span>
                            @endif
                            <span class="lki d-flex">
                                Onboarding
                            </span>
                        </a>
                        <?php
                        $requiredFields = 0;
                        if (!isset(Auth::user()->influencerDetail)) {
                            $requiredFields = 8;
                        } else {
                            if (Auth::user()->influencerDetail->category_id == null) {
                                $requiredFields += 1;
                            }
                            if (count(Auth::user()->hashtags) != 3) {
                                $requiredFields += 3;
                            }
                            if (Auth::user()->influencerDetail->influencer_type == null) {
                                $requiredFields += 1;
                            }
                            if (Auth::user()->influencerDetail->ages == null) {
                                $requiredFields += 1;
                            }
                            if (Auth::user()->influencerDetail->content_language == null) {
                                $requiredFields += 1;
                            }
                            if (Auth::user()->influencerDetail->content_attracts == null) {
                                $requiredFields += 1;
                            }
                        }
                        $optionalFields = 0;
                        ?>
                    </li>
                    <li class="@if (isset(Auth::user()->influencerDetail->publish) && Auth::user()->influencerDetail->publish == 'Publish') @else desabled @endif ">
                        <a href="{{ url('my-rank') }}" class="{{ Request::is('my-rank') ? 'active' : '' }}">
                            <img src="{{ asset('/assets/front-end/images/icons/sidemenu-analysis-mobile.svg') }}"
                                alt="">
                            <span class="lki">My Rank</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    @endif
    @if (Auth::user()->user_type == 'customer')
        <div class="menu-influncer">
            <div class="fixed-menu">
                <ul class="">
                    <li class="home-link">
                        <a href="{{ url('/home') }}" class="{{ Request::is('/home') ? 'active' : '' }}">
                            <img src="{{ asset('/assets/front-end/images/icons/home_mobile.svg') }}"
                                alt="">
                            <span class="lki">Home
                            </span></a>
                    </li>
                    <li>
                        <a href="{{ url('active-campaigns') }}"
                            class="{{ Request::is('active-campaigns') ? 'active' : '' }}">
                            <img src="{{ asset('/assets/front-end/images/icons/active_campaign_mobile.svg') }}"
                                alt="" style="filter: grayscale(100%) brightness(0);">
                            <span class="lki">Active Campaigns</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ url('create-campaign') }}"
                            class="{{ Request::is('create-campaign') ? 'active' : '' }}">
                            <img src="{{ asset('/assets/front-end/images/icons/campaign_mobile.svg') }}" alt="">
                            <span class="lki" style="color: black !important;">Start Campaign</span>
                        </a>
                    </li>
                    <li
                        class="@if (Auth::user()->user_type == 'influencer') @if (isset(Auth::user()->influencerDetail->publish) && Auth::user()->influencerDetail->publish == 'Publish')  @else  desabled @endif @endif ">
                        <a href="{{ url('open-campaigns') }}"
                            class="{{ Request::is('open-campaigns') ? 'active' : '' }}">
                            <img src="{{ asset('/assets/front-end/images/icons/stats_mobile.svg') }}"
                                alt="">
                            <span class="lki">Open Campaigns</span>
                        </a>
                    </li>
                    <li class="extramenu">
                        <a href="javascript:void(0)" class="open-extra">
                            <img src="{{ asset('/assets/front-end/images/icons/more_mobile.svg') }}"
                                class="image_more" alt="">
                            <span class="lki">More</span>
                        </a>
                    </li>
                </ul>
            </div>
            <div class="extra-menu">
                <ul class="">
                    <li class=" desabled ">
                        <a href="{{ url('my-rank') }}" class="{{ Request::is('my-rank') ? 'active' : '' }}">
                            <img src="{{ asset('/assets/front-end/images/icons/sidemenu-analysis-mobile.svg') }}"
                                alt="">
                            <span class="lki">Analysis</span>
                        </a>
                    </li>
                    <li>
                        <a @if (Request::is('my-profile')) href="javascript:void(0)" @else href="{{ url('my-profile') }}" @endif
                            class="{{ Request::is('my-profile') ? 'active' : '' }}">
                            <img src="{{ asset('/assets/front-end/images/icons/profile_info_new.svg') }}" alt=""
                                style="height: 24px;width:24px;">
                            <span class="lki d-flex">Profile Information
                            </span>
                        </a>
                        <?php
                        $requiredFields = 0;
                        if (Auth::user()->first_name == null) {
                            $requiredFields += 1;
                        }
                        if (Auth::user()->last_name == null) {
                            $requiredFields += 1;
                        }
                        if (Auth::user()->country == null) {
                            $requiredFields += 1;
                        }
                        // if(Auth::user()->state == null)
                        // {
                        //     $requiredFields += 1;
                        // }
                        if (Auth::user()->city == null) {
                            $requiredFields += 1;
                        }

                        $optionalFields = 0;
                        if (Auth::user()->phone == null) {
                            $optionalFields += 1;
                        }

                        if (Auth::user()->company_name == null) {
                            $optionalFields += 1;
                        }

                        if (Auth::user()->street == null) {
                            $optionalFields += 1;
                        }

                        if (Auth::user()->zip_code == null) {
                            $optionalFields += 1;
                        }

                        ?>
                    </li>
                </ul>
            </div>
        </div>
    @endif

</div>
<script>
    $(document).ready(function() {
        $(".dashMenu").click(function() {
            $(".side-profile").toggleClass("expand");
        });



        if (document.getElementById('user_status') && document.getElementById('user_status').checked) {
            $("#user_status").next("label").html(
                "<img src='./assets/front-end/images/icons/icon-wifi.svg'> <span>Online</span>")
            $(".dashboardsidebar").addClass("colOnline");
            $(".dashboardsidebar").removeClass("colOffline");
        } else {
            $("#user_status").next("label").html(
                "<img src='./assets/front-end/images/icons/icon-wifi.svg'> <span>Offline</span>");
            $(".dashboardsidebar").addClass("colOffline");
            $(".dashboardsidebar").removeClass("colOnline");
        }


        $(document).on("click", "#user_status", function() {
            if ($(window).width() <= 1199) {
                $(".menuoverlay").hide();
                $(".dashboard-left").toggleClass("open");
                $(".dashboard-right").toggleClass("open");
            }
            console.log($('#published').val());
            // if($('#verified').val() == '' ){
            //      $('#verifyModal').modal('show');
            //      if($(this).prop("checked"))
            //      $(this).prop("checked", false);
            //      else
            //      $(this).prop("checked", true);
            // }
            if ($('#published').val() != 'Publish') {
                $('#publishModal').modal('show');
            } else {
                if ($('#req_count').val() >= 5) {
                    $('#requestModal').modal('show');
                } else {
                    if (document.getElementById('user_status').checked) {
                        $.ajax({
                            type: "get",
                            url: "{{ url('/change-status') }}/1",
                            success: function(response) {
                                console.log(response);


                                if (response.status == "success") {
                                    toastr.success(response.msg);

                                    setTimeout(function() {
                                        location.reload();
                                    }, 5000)
                                }
                                if (response.status == "error") {
                                    toastr.info(response.msg);
                                    setTimeout(function() {
                                        location.reload();
                                    }, 5000)
                                }
                                swal("Success!", response.msg, "success");
                                $("#user_status").next("label").html(
                                    "<img src='./assets/front-end/images/icons/icon-wifi.svg'> <span>Online</span>"
                                );
                                $(".dashboardsidebar").addClass("colOnline");
                                $(".dashboardsidebar").removeClass("colOffline");
                                $('#usr_offline').hide();
                                $('#usr_online').show();

                            }
                        });
                    } else {

                        $.ajax({
                            type: "get",
                            url: "{{ url('/change-status') }}/0",
                            success: function(response) {
                                console.log(response);


                                if (response.status == "success") {
                                    toastr.success(response.msg);

                                    setTimeout(function() {
                                        location.reload();
                                    }, 5000)
                                }
                                if (response.status == "error") {
                                    toastr.info(response.msg);
                                    setTimeout(function() {
                                        location.reload();
                                    }, 5000)
                                }
                                swal("Success!", response.msg, "success");
                                $("#user_status").next("label").html(
                                    "<img src='./assets/front-end/images/icons/icon-wifi.svg'> <span>Offline</span>"
                                );
                                $(".dashboardsidebar").addClass("colOffline");
                                $(".dashboardsidebar").removeClass("colOnline");
                                $('#usr_offline').show();
                                $('#usr_online').hide();
                            }
                        });
                    }
                }

            }


        })

    });
</script>
